const { processHalmos } = require("./lib/cjs/halmos/logParser");

// Test the updated parser with your provided log data
const testLogs = `Assertion failure detected in CryticToFoundry.breaksOnZero(address)
Counterexample: 
    halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_dda8eb0_17 = 0x00
    p_x_address_73a2fb5_18 = 0x00
Sequence:
    CALL CryticToFoundry::breaksOnZero(p_x_address_73a2fb5_18) (value: 
halmos_msg_value_0x7fa9385be102ac3eac297483dd6233d62b3e1496_dda8eb0_17) (caller: 
halmos_msg_sender_0x7fa9385be102ac3eac297483dd6233d62b3e1496_41050af_16)
        SLOAD  @0 → 0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496
        CALL hevm::prank(0x0000000000000000000000007fa9385be102ac3eac297483dd6233d62b3e1496) (caller: 
CryticToFoundry)
        ↩ 0x
    ↩ STATICCALL 0x (error: FailCheatcode("VmAssertion(cond=Not(Extract(159, 0, p_x_address_73a2fb5_18) == 0), 
msg='x is zero')"))

[PASS] invariant_number_never_zero() (paths: 100, time: 2.07s, bounds: [])`;

console.log("Testing getHalmosPropertyAndSequence with assertion failure logs:");
const results = getHalmosPropertyAndSequence(testLogs);
console.log("Results:", JSON.stringify(results, null, 2));

if (results.length > 0) {
    console.log("\n✅ SUCCESS: Parser correctly detected assertion failure in passed test!");
    console.log(`Found broken property: ${results[0].brokenProperty}`);
    console.log(`Counterexample parameters: ${results[0].sequence.length} items`);
} else {
    console.log("\n❌ FAILED: Parser did not detect the assertion failure");
}
