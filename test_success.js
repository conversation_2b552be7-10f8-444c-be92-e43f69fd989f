const fs = require("fs");
const { halmosLogsToFunctions } = require("./lib/cjs/index.js");
const {
  getHalmosPropertyAndSequence,
} = require("./lib/cjs/halmos/logParser.js");

const logs = fs.readFileSync("./src/halmos/logs.txt", "utf8");

// First check what sequences we're getting
const sequences = getHalmosPropertyAndSequence(logs);
const amtAbove0Sequences = sequences.filter((p) =>
  p.brokenProperty.includes("invariant_amt_isAbove0")
);

console.log("=== DEBUGGING SEQUENCES ===");
amtAbove0Sequences.forEach((seq, i) => {
  console.log(`Sequence ${i + 1}:`);
  console.log("Property:", seq.brokenProperty);
  console.log(
    "Sequence length:",
    Array.isArray(seq.sequence) ? seq.sequence.length : seq.sequence.length
  );
  console.log("Sequence content:", seq.sequence);
  console.log("---");
});

// Generate the functions
const functions = halmosLogsToFunctions(logs, "test_run");

// Find the invariant_amt_isAbove0 function
const amtAbove0Function = functions
  .split("\n\n")
  .find((f) => f.includes("invariant_amt_isAbove0"));

if (amtAbove0Function) {
  console.log("Generated function:");
  console.log(amtAbove0Function);

  // Check if it contains switchActor and the correct simple structure
  const hasSwitchActor = amtAbove0Function.includes("switchActor");
  const hasPermit = amtAbove0Function.includes("permit");
  const hasEntropyParam = amtAbove0Function.includes("entropy_uint256");

  console.log("\nAnalysis:");
  console.log("Contains switchActor:", hasSwitchActor);
  console.log("Contains permit (should be false):", hasPermit);
  console.log("Has entropy parameter:", hasEntropyParam);

  if (hasSwitchActor && !hasPermit && hasEntropyParam) {
    console.log("\n🎉 SUCCESS: Generated the correct simple switchActor test!");
  } else {
    console.log("\n❌ ISSUE: Still generating wrong test");
  }
} else {
  console.log("invariant_amt_isAbove0 function not found");
}
